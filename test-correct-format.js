// Test the correct DoubleTick API format directly

const testCorrectFormat = async () => {
  const url = 'https://public.doubletick.io/whatsapp/message/template';
  const headers = {
    'Authorization': 'key_1SKycvfqmc',
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  };

  // Test the exact format from DoubleTick documentation
  const testCases = [
    {
      name: 'Order Processing',
      payload: {
        messages: [
          {
            content: {
              language: 'en',
              templateData: {
                header: {
                  type: 'TEXT',
                  placeholder: '12345-test-order'
                },
                body: {
                  placeholders: []
                }
              },
              templateName: 'order_processing'
            },
            from: '+918870001978',
            to: '+917902467075'
          }
        ]
      }
    },
    {
      name: 'Order Cancelled (with reason)',
      payload: {
        messages: [
          {
            content: {
              language: 'en',
              templateData: {
                header: {
                  type: 'TEXT',
                  placeholder: '12345-test-order'
                },
                body: {
                  placeholders: ['Customer requested cancellation']
                }
              },
              templateName: 'order_cancelled'
            },
            from: '+918870001978',
            to: '+917902467075'
          }
        ]
      }
    },
    {
      name: 'Order Out for Delivery',
      payload: {
        messages: [
          {
            content: {
              language: 'en',
              templateData: {
                header: {
                  type: 'TEXT',
                  placeholder: '12345-test-order'
                },
                body: {
                  placeholders: []
                }
              },
              templateName: 'order_out_for_delivery'
            },
            from: '+918870001978',
            to: '+917902467075'
          }
        ]
      }
    },
    {
      name: 'Order Delivered',
      payload: {
        messages: [
          {
            content: {
              language: 'en',
              templateData: {
                header: {
                  type: 'TEXT',
                  placeholder: '12345-test-order'
                },
                body: {
                  placeholders: []
                }
              },
              templateName: 'order_delivered'
            },
            from: '+918870001978',
            to: '+917902467075'
          }
        ]
      }
    }
  ];

  console.log('Testing correct DoubleTick API format...\n');

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    
    console.log(`--- Test ${i + 1}: ${testCase.name} ---`);
    console.log('Payload:', JSON.stringify(testCase.payload, null, 2));

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(testCase.payload)
      });

      const responseText = await response.text();
      console.log('Status:', response.status);
      console.log('Response:', responseText);

      if (response.ok) {
        try {
          const responseData = JSON.parse(responseText);
          const message = responseData.messages?.[0];
          
          if (message?.status === 'SENT' || message?.status === 'QUEUED') {
            console.log('✅ SUCCESS! Template sent successfully!');
          } else if (message?.status === 'FAILED') {
            console.log('❌ FAILED:', message.errorMessage);
          } else {
            console.log('🔍 Unknown status:', message?.status);
          }
        } catch (parseError) {
          console.log('❌ Could not parse response as JSON');
        }
      } else {
        console.log('❌ HTTP Error:', response.status, response.statusText);
      }

      console.log(''); // Empty line for readability
      
      // Wait 3 seconds between requests to avoid rate limiting and spam
      if (i < testCases.length - 1) {
        console.log('Waiting 3 seconds before next test...\n');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }

    } catch (error) {
      console.log('❌ Request failed:', error.message);
      console.log(''); // Empty line for readability
    }
  }

  console.log('🎉 Test completed!');
};

// Run the test
testCorrectFormat().catch(console.error);
