import { serve } from 'https://deno.land/std@0.168.0/http/server.ts';
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type'
};

// Enhanced logger function
const log = (message: string, data: any) => {
  console.log(JSON.stringify({
    timestamp: new Date().toISOString(),
    message,
    data
  }));
};

// New: send a WhatsApp template message via DoubleTick using correct API format
async function sendWhatsAppTemplate(phone: string, templateName: string, orderId: string, bodyPlaceholders: string[] = []) {
  log('sendWhatsAppTemplate called', { phone, templateName, orderId, bodyPlaceholders });

  const url = 'https://public.doubletick.io/whatsapp/message/template';
  const headers = {
    'Authorization': 'key_1SKycvfqmc',
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  };

  // Use the correct DoubleTick API format
  const payload = {
    messages: [
      {
        from: '+918870001978',
        to: phone,
        content: {
          language: 'en',
          templateName,
          templateData: {
            header: {
              type: 'TEXT',
              placeholder: orderId
            },
            body: {
              placeholders: bodyPlaceholders
            }
          }
        }
      }
    ]
  };

  log('DoubleTick API request with correct format', { url, payload });

  try {
    log('Making fetch request to DoubleTick API', {});
    const res = await fetch(url, {
      method: 'POST',
      headers,
      body: JSON.stringify(payload)
    });

    log('Fetch completed, parsing response', { status: res.status, statusText: res.statusText });
    const text = await res.text();
    log('Template API response', { status: res.status, statusText: res.statusText, body: text });

    if (!res.ok) {
      log('API request failed', { status: res.status, statusText: res.statusText, body: text });
      throw new Error(`Template send failed: ${res.status} - ${text}`);
    }

    // Check if the response indicates success
    const responseData = JSON.parse(text);
    const message = responseData.messages?.[0];

    if (message?.status === 'SENT' || message?.status === 'QUEUED') {
      log('Template sent successfully', { status: res.status, body: text });
      return res;
    } else if (message?.status === 'FAILED') {
      log('Template send failed', { status: res.status, body: text, errorMessage: message.errorMessage });
      throw new Error(`Template send failed: ${message.errorMessage}`);
    }

    log('Template sent successfully', { status: res.status, body: text });
    return res;
  } catch (err: any) {
    log('Template API error', { error: err.message, stack: err.stack });
    throw err;
  }
}

serve(async (req) => {
  // Log every request that comes in
  log('Function invoked', {
    method: req.method,
    url: req.url,
    headers: Object.fromEntries(req.headers.entries())
  });

  if (req.method === 'OPTIONS') {
    log('Handling OPTIONS request', {});
    return new Response('ok', { headers: corsHeaders });
  }

  try {
    log('Attempting to parse request body', {});
    const body = await req.json();
    log('Successfully parsed webhook payload', body);

    const { order_id, status } = body;
    log('Extracted fields from payload', { order_id, status });

    if (!order_id || !status) {
      log('Missing required fields', { order_id, status });
      throw new Error('Missing required fields: order_id or status');
    }

    if (status === 'New') {
      log('Skipping New status', { order_id, status });
      return new Response(JSON.stringify({ success: true, message: 'Skipped New status' }), {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200
      });
    }

    // Fetch order + customer data
    log('Creating Supabase client', {});
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    log('Fetching order data from database', { order_id });
    const { data: order, error } = await supabase
      .from('orders')
      .select(`
        id,
        cancellation_reason,
        customers ( name, whatsapp_number )
      `)
      .eq('id', order_id)
      .single();

    log('Database query result', { order, error });

    if (error || !order) {
      log('Error fetching order or order not found', { error, order });
      throw new Error(`Failed to fetch order details: ${error?.message || 'Order not found'}`);
    }

    const phone = order.customers?.whatsapp_number;
    log('Extracted phone number', { phone, customer_data: order.customers });

    if (!phone) {
      log('WhatsApp number not found', { order });
      throw new Error('WhatsApp number not found');
    }

    // Determine which template to send
    log('Determining template for status', { status });
    let templateName: string;
    let bodyPlaceholders: string[] = [];

    // Get customer name for templates
    const customerName = order.customers?.name || 'Customer';

    switch (status) {
      case 'Processing':
        templateName = 'orderprocessingv1';
        bodyPlaceholders = []; // No body placeholders needed for processing
        log('Selected Processing template', { templateName, bodyPlaceholders });
        break;
      case 'Out for Delivery':
        templateName = 'out_for_delivery';
        bodyPlaceholders = []; // No body placeholders needed for out for delivery
        log('Selected Out for Delivery template', { templateName, bodyPlaceholders });
        break;
      case 'Delivered':
        templateName = 'orderdeliveredv1';
        bodyPlaceholders = []; // No body placeholders needed for delivered
        log('Selected Delivered template', { templateName, bodyPlaceholders });
        break;
      case 'Cancelled':
        templateName = 'order_cancelled_v2';
        bodyPlaceholders = [order.cancellation_reason ?? 'Order cancelled as requested']; // Cancellation reason in body
        log('Selected Cancelled template', { templateName, bodyPlaceholders });
        break;
      default:
        log('Unsupported status received', { status });
        throw new Error(`Unsupported status: ${status}`);
    }

    // Log the template details before sending
    log('Sending WhatsApp template', {
      phone,
      templateName,
      orderId: order_id,
      bodyPlaceholders,
      status
    });

    // Send the template
    log('About to call sendWhatsAppTemplate', {});
    await sendWhatsAppTemplate(phone, templateName, order_id.toString(), bodyPlaceholders);
    log('Successfully sent WhatsApp template', {});

    const successResponse = { success: true, message: 'Template sent', order_id, status };
    log('Returning success response', successResponse);
    return new Response(JSON.stringify(successResponse), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 200
    });
  } catch (err: any) {
    log('Error processing webhook', {
      error: err.message,
      stack: err.stack,
      name: err.name
    });
    const errorResponse = { success: false, error: err.message };
    return new Response(JSON.stringify(errorResponse), {
      headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      status: 500
    });
  }
});
