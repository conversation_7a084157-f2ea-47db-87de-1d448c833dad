// Test script to check what templates are available in DoubleTick and their parameter requirements

const testDoubleTick = async () => {
  const url = 'https://public.doubletick.io/whatsapp/message/template';
  const headers = {
    'Authorization': 'key_1SKycvfqmc',
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  };

  // Test different template names and parameter combinations
  const testCases = [
    // Test if templates exist with different names
    { templateName: 'order_processing', parameters: [] },
    { templateName: 'order_processing', parameters: [{ type: 'text', text: 'Test Customer' }] },
    { templateName: 'order_processing', parameters: [{ type: 'text', text: 'Test Customer' }, { type: 'text', text: '12345' }] },
    { templateName: 'order_processing', parameters: [{ type: 'text', text: '12345' }] },
    
    // Try alternative template names
    { templateName: 'order_confirmation', parameters: [{ type: 'text', text: 'Test Customer' }, { type: 'text', text: '12345' }] },
    { templateName: 'order_update', parameters: [{ type: 'text', text: 'Test Customer' }, { type: 'text', text: '12345' }] },
    { templateName: 'processing', parameters: [{ type: 'text', text: 'Test Customer' }, { type: 'text', text: '12345' }] },
    
    // Test delivery templates
    { templateName: 'order_out_for_delivery', parameters: [] },
    { templateName: 'order_out_for_delivery', parameters: [{ type: 'text', text: 'Test Customer' }] },
    { templateName: 'order_out_for_delivery', parameters: [{ type: 'text', text: 'Test Customer' }, { type: 'text', text: '12345' }] },
    { templateName: 'order_out_for_delivery', parameters: [{ type: 'text', text: 'Test Customer' }, { type: 'text', text: '12345' }, { type: 'text', text: 'On the way' }] },
    
    // Test delivered templates
    { templateName: 'order_delivered', parameters: [] },
    { templateName: 'order_delivered', parameters: [{ type: 'text', text: 'Test Customer' }] },
    { templateName: 'order_delivered', parameters: [{ type: 'text', text: 'Test Customer' }, { type: 'text', text: '12345' }] },
    
    // Test cancelled templates
    { templateName: 'order_cancelled', parameters: [] },
    { templateName: 'order_cancelled', parameters: [{ type: 'text', text: 'Test Customer' }] },
    { templateName: 'order_cancelled', parameters: [{ type: 'text', text: 'Test Customer' }, { type: 'text', text: '12345' }] },
    { templateName: 'order_cancelled', parameters: [{ type: 'text', text: 'Test Customer' }, { type: 'text', text: '12345' }, { type: 'text', text: 'Cancelled by customer' }] },
  ];

  const testPhone = '917902467075'; // Use the same phone number from your logs

  console.log('Testing DoubleTick templates...');
  console.log('Phone number:', testPhone);
  console.log('Total test cases:', testCases.length);
  console.log('');

  for (let i = 0; i < testCases.length; i++) {
    const testCase = testCases[i];
    
    console.log(`\n--- Test ${i + 1}/${testCases.length} ---`);
    console.log('Template:', testCase.templateName);
    console.log('Parameters:', testCase.parameters.length, testCase.parameters);

    const payload = {
      messages: [
        {
          from: '+918870001978',
          to: testPhone,
          content: {
            language: 'en',
            templateName: testCase.templateName,
            parameters: testCase.parameters
          }
        }
      ]
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      });

      const responseText = await response.text();
      console.log('Status:', response.status);
      console.log('Response:', responseText);

      if (response.ok) {
        try {
          const responseData = JSON.parse(responseText);
          const message = responseData.messages?.[0];
          
          if (message?.status === 'SENT' || message?.status === 'QUEUED') {
            console.log('✅ SUCCESS! This template configuration works!');
            console.log('Template Name:', testCase.templateName);
            console.log('Parameter Count:', testCase.parameters.length);
            console.log('Parameters:', testCase.parameters);
            
            // Don't send more successful messages to avoid spam
            console.log('\n🎉 Found working template! Stopping tests to avoid spam.');
            return;
          } else if (message?.errorMessage) {
            console.log('❌ Error:', message.errorMessage);
          }
        } catch (parseError) {
          console.log('❌ Could not parse response as JSON');
        }
      }

      // Wait 2 seconds between requests to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      console.log('❌ Request failed:', error.message);
    }
  }

  console.log('\n📋 Test completed. Check the results above to see which template configurations work.');
};

// Run the test
testDoubleTick().catch(console.error);
