// Test DoubleTick templates with more parameters (4-10 parameters)

const testMoreParameters = async () => {
  const url = 'https://public.doubletick.io/whatsapp/message/template';
  const headers = {
    'Authorization': 'key_1SKycvfqmc',
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  };

  const testPhone = '917902467075';
  
  // Test with increasing number of parameters
  const baseParams = [
    { type: 'text', text: 'Test Customer' },      // {{1}} - Customer Name
    { type: 'text', text: '12345' },              // {{2}} - Order ID
    { type: 'text', text: 'Processing' },         // {{3}} - Status
    { type: 'text', text: 'Dhanam Supermarket' }, // {{4}} - Store Name
    { type: 'text', text: '₹118' },               // {{5}} - Amount
    { type: 'text', text: '2 items' },            // {{6}} - Item count
    { type: 'text', text: 'Today' },              // {{7}} - Delivery date
    { type: 'text', text: '10:30 AM' },           // {{8}} - Time
    { type: 'text', text: 'Cash on Delivery' },   // {{9}} - Payment method
    { type: 'text', text: 'Thank you!' }          // {{10}} - Closing message
  ];

  // Test only order_processing template with different parameter counts
  console.log('Testing order_processing template with different parameter counts...\n');

  for (let paramCount = 4; paramCount <= 10; paramCount++) {
    const parameters = baseParams.slice(0, paramCount);
    
    console.log(`--- Testing with ${paramCount} parameters ---`);
    console.log('Parameters:', parameters.map((p, i) => `{{${i+1}}} = "${p.text}"`).join(', '));

    const payload = {
      messages: [
        {
          from: '+918870001978',
          to: testPhone,
          content: {
            language: 'en',
            templateName: 'order_processing',
            parameters: parameters
          }
        }
      ]
    };

    try {
      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      });

      const responseText = await response.text();
      console.log('Status:', response.status);
      console.log('Response:', responseText);

      if (response.ok) {
        try {
          const responseData = JSON.parse(responseText);
          const message = responseData.messages?.[0];
          
          if (message?.status === 'SENT' || message?.status === 'QUEUED') {
            console.log('✅ SUCCESS! Found the correct parameter count!');
            console.log(`🎯 order_processing template needs exactly ${paramCount} parameters`);
            console.log('Parameters needed:');
            parameters.forEach((p, i) => {
              console.log(`  {{${i+1}}} = "${p.text}"`);
            });
            
            console.log('\n🛑 Stopping test to avoid sending more messages.');
            return paramCount;
          } else if (message?.errorMessage) {
            console.log('❌ Error:', message.errorMessage);
            
            if (!message.errorMessage.includes('Too few variables')) {
              console.log('🔍 Different error - might be close to correct parameter count');
            }
          }
        } catch (parseError) {
          console.log('❌ Could not parse response as JSON');
        }
      }

      console.log(''); // Empty line for readability
      
      // Wait 2 seconds between requests
      await new Promise(resolve => setTimeout(resolve, 2000));

    } catch (error) {
      console.log('❌ Request failed:', error.message);
    }
  }

  console.log('\n📋 Test completed. If no success was found, the template might need more than 10 parameters.');
  console.log('💡 Check your DoubleTick dashboard to see the exact template configuration.');
  
  return null;
};

// Run the test
testMoreParameters().then(result => {
  if (result) {
    console.log(`\n🎉 Found that order_processing needs ${result} parameters!`);
  } else {
    console.log('\n❌ Could not determine the correct parameter count.');
  }
}).catch(console.error);
