// Test script to trigger the database trigger by updating order status
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  'https://phmbblwthsregggkwbfo.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBobWJibHd0aHNyZWdnZ2t3YmZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1MDkzNDAsImV4cCI6MjA1OTA4NTM0MH0.1Obru9SaNCGLN9wdkhVsuuUjBjH5fIVJOdalByvv7Yo'
);

const testTrigger = async () => {
  // Get a real order ID from the database
  console.log('Finding an order to test with...');
  const { data: orders, error: findError } = await supabase
    .from('orders')
    .select('id, status, customers(whatsapp_number)')
    .limit(1);

  if (findError || !orders || orders.length === 0) {
    console.error('No orders found to test with:', findError);
    return;
  }

  const testOrderId = orders[0].id;

  console.log('Testing database trigger...');
  console.log('Order ID:', testOrderId);

  try {
    // First, get the current order to see its current status
    console.log('\n1. Fetching current order status...');
    const { data: currentOrder, error: fetchError } = await supabase
      .from('orders')
      .select('id, status, customers(whatsapp_number)')
      .eq('id', testOrderId)
      .single();

    if (fetchError) {
      console.error('Error fetching order:', fetchError);
      return;
    }

    console.log('Current order:', currentOrder);

    // Update the order status to trigger the webhook
    console.log('\n2. Updating order status to trigger webhook...');
    const newStatus = 'Processing'; // Change this to test different statuses

    const { data: updatedOrder, error: updateError } = await supabase
      .from('orders')
      .update({
        status: newStatus,
        updated_at: new Date().toISOString()
      })
      .eq('id', testOrderId)
      .select();

    if (updateError) {
      console.error('Error updating order:', updateError);
      return;
    }

    console.log('Order updated successfully:', updatedOrder);
    console.log('\n3. Check the Supabase function logs to see if the WhatsApp template function was triggered.');
    console.log('   Go to: https://supabase.com/dashboard/project/phmbblwthsregggkwbfo/functions');
    console.log('   Click on "whatsapp-template" and check the logs tab.');

  } catch (error) {
    console.error('Unexpected error:', error);
  }
};

// Test with different status transitions
const testStatusTransitions = async () => {
  const testOrderId = '799996ea-9e3d-4180-8723-2ebabda9d90f';
  const statuses = ['Processing', 'Out for Delivery', 'Delivered'];

  console.log('Testing multiple status transitions...');

  for (let i = 0; i < statuses.length; i++) {
    const status = statuses[i];
    console.log(`\n=== Updating to status: ${status} ===`);

    try {
      const { data, error } = await supabase
        .from('orders')
        .update({
          status: status,
          updated_at: new Date().toISOString()
        })
        .eq('id', testOrderId)
        .select();

      if (error) {
        console.error(`Error updating to ${status}:`, error);
      } else {
        console.log(`Successfully updated to ${status}`);

        // Wait 3 seconds between updates to see logs clearly
        if (i < statuses.length - 1) {
          console.log('Waiting 3 seconds before next update...');
          await new Promise(resolve => setTimeout(resolve, 3000));
        }
      }
    } catch (error) {
      console.error(`Unexpected error updating to ${status}:`, error);
    }
  }

  console.log('\nAll status updates completed. Check the function logs!');
};

// Run the test
console.log('Choose test type:');
console.log('1. Single status update');
console.log('2. Multiple status transitions');

// For now, run single test
testTrigger();

// Uncomment to test multiple transitions:
// testStatusTransitions();
