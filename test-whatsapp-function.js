// Test script to manually trigger the WhatsApp template function
// Run this with: node test-whatsapp-function.js

const testFunction = async () => {
  const functionUrl = 'https://phmbblwthsregggkwbfo.supabase.co/functions/v1/whatsapp-template';

  // Test payload - replace with a real order ID from your database
  const testPayload = {
    order_id: '799996ea-9e3d-4180-8723-2ebabda9d90f', // Replace with actual order ID
    status: 'Processing'
  };

  console.log('Testing WhatsApp template function...');
  console.log('Function URL:', functionUrl);
  console.log('Test payload:', testPayload);

  try {
    const response = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBobWJibHd0aHNyZWdnZ2t3YmZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1MDkzNDAsImV4cCI6MjA1OTA4NTM0MH0.1Obru9SaNCGLN9wdkhVsuuUjBjH5fIVJOdalByvv7Yo'
      },
      body: JSON.stringify(testPayload)
    });

    const responseText = await response.text();

    console.log('\n--- Response ---');
    console.log('Status:', response.status);
    console.log('Status Text:', response.statusText);
    console.log('Response Body:', responseText);

    if (!response.ok) {
      console.error('Function call failed!');
    } else {
      console.log('Function call successful!');
    }

  } catch (error) {
    console.error('Error calling function:', error.message);
  }
};

// Test with different statuses
const testAllStatuses = async () => {
  const statuses = ['Processing', 'Out for Delivery', 'Delivered', 'Cancelled'];
  const orderId = '799996ea-9e3d-4180-8723-2ebabda9d90f'; // Replace with actual order ID

  for (const status of statuses) {
    console.log(`\n=== Testing status: ${status} ===`);

    const testPayload = {
      order_id: orderId,
      status: status
    };

    try {
      const response = await fetch('https://phmbblwthsregggkwbfo.supabase.co/functions/v1/whatsapp-template', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InBobWJibHd0aHNyZWdnZ2t3YmZvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM1MDkzNDAsImV4cCI6MjA1OTA4NTM0MH0.1Obru9SaNCGLN9wdkhVsuuUjBjH5fIVJOdalByvv7Yo'
        },
        body: JSON.stringify(testPayload)
      });

      const responseText = await response.text();
      console.log(`Status: ${response.status}, Response: ${responseText}`);

      // Wait a bit between requests
      await new Promise(resolve => setTimeout(resolve, 1000));

    } catch (error) {
      console.error(`Error testing ${status}:`, error.message);
    }
  }
};

// Run the test
console.log('Choose test type:');
console.log('1. Single test (Processing status)');
console.log('2. Test all statuses');

// For now, just run single test
testFunction();

// Uncomment to test all statuses:
// testAllStatuses();
